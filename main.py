import cv2
import numpy as np
import matplotlib.pyplot as plt
import os
from scipy.ndimage import gaussian_filter
from scipy.signal import find_peaks
from sklearn.cluster import DBSCAN
import json
from datetime import datetime
import logging
import multiprocessing as mp
from concurrent.futures import ThreadPoolExecutor
import threading


class NumpyEncoder(json.JSONEncoder):
    """Custom JSON encoder for numpy types"""

    def default(self, obj):
        if isinstance(obj, np.ndarray):
            return obj.tolist()
        if isinstance(obj, np.integer):
            return int(obj)
        if isinstance(obj, np.floating):
            return float(obj)
        if isinstance(obj, np.bool_):
            return bool(obj)
        return super().default(obj)


class AdvancedVideoAnalyzer:
    """
    Advanced video analysis system for detecting and characterizing events
    based on multiple visual and temporal features.
    """

    def __init__(self, config=None):
        """
        Initialize the analyzer with configuration parameters.
        """
        self.config = config or self._default_config()
        self.setup_logging()
        self.setup_gpu_acceleration()

        # Analysis results storage
        self.results = {
            'temporal_features': [],
            'spatial_features': [],
            'motion_characteristics': [],
            'intensity_profiles': [],
            'event_classifications': []
        }

    def _default_config(self):
        """Default configuration parameters optimized for performance"""
        return {
            'flow_params': {
                'pyr_scale': 0.5,
                'levels': 2,  # Reduced for speed
                'winsize': 10,  # Smaller window for speed
                'iterations': 2,  # Fewer iterations for speed
                'poly_n': 5,
                'poly_sigma': 1.2,
                'flags': cv2.OPTFLOW_FARNEBACK_GAUSSIAN
            },
            'frame_skip': 2,  # Skip more frames for speed
            'roi_detection': {
                'brightness_threshold': 0.8,  # For flash detection
                'size_threshold': 0.01,       # Minimum event size
                'temporal_window': 5          # Frames to analyze around event
            },
            'classification_features': {
                'analyze_flash_duration': True,
                'analyze_smoke_pattern': True,
                'analyze_debris_trajectory': True,
                'analyze_shockwave_propagation': True
            },
            'use_gpu': True,  # Enable GPU acceleration where possible
            'num_threads': min(8, mp.cpu_count()),  # Use multiple threads
            'resize_factor': 0.5  # Resize frames for faster processing
        }

    def setup_logging(self):
        """Setup logging for the analysis"""
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s'
        )
        self.logger = logging.getLogger(__name__)

    def setup_gpu_acceleration(self):
        """Setup GPU acceleration and optimizations"""
        # Set OpenCV to use optimized code paths
        cv2.setUseOptimized(True)

        # Set number of threads for OpenCV
        cv2.setNumThreads(self.config.get('num_threads', 4))

        # Check for GPU support
        self.use_gpu = False
        if hasattr(cv2, 'cuda') and cv2.cuda.getCudaEnabledDeviceCount() > 0:
            self.use_gpu = True
            self.logger.info("GPU acceleration enabled")
        else:
            self.logger.info("Using CPU optimization with {} threads".format(
                cv2.getNumThreads()))

        # Enable Intel IPP optimizations if available
        if cv2.useOptimized():
            self.logger.info("OpenCV optimizations enabled")

    def analyze_video(self, video_path="video/test.mp4", output_dir='advanced_analysis'):
        """
        Main analysis function that processes the entire video.
        """
        if not os.path.exists(output_dir):
            os.makedirs(output_dir)

        cap = cv2.VideoCapture(video_path)
        if not cap.isOpened():
            raise ValueError(f"Error opening video: {video_path}")

        # Get video properties
        fps = cap.get(cv2.CAP_PROP_FPS)
        total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))

        self.logger.info(
            f"Analyzing video: {total_frames} frames at {fps} FPS")

        # Initialize analysis components
        prev_frame = None
        frame_index = 0
        event_candidates = []

        while True:
            ret, frame = cap.read()
            if not ret:
                break

            if frame_index % self.config['frame_skip'] == 0:
                # Resize frame for faster processing if configured
                if 'resize_factor' in self.config and self.config['resize_factor'] < 1.0:
                    height, width = frame.shape[:2]
                    new_height = int(height * self.config['resize_factor'])
                    new_width = int(width * self.config['resize_factor'])
                    frame = cv2.resize(frame, (new_width, new_height),
                                       interpolation=cv2.INTER_LINEAR)

                # Multi-modal analysis
                frame_results = self._analyze_frame(
                    frame, prev_frame, frame_index, fps
                )

                # Store results
                if frame_results['event_detected']:
                    event_candidates.append({
                        'frame': frame_index,
                        'timestamp': frame_index / fps,
                        'features': frame_results
                    })

                # Update analysis results
                self._update_results(frame_results)

                prev_frame = frame.copy()

            frame_index += 1

            if frame_index % 50 == 0:  # More frequent updates
                self.logger.info(
                    f"Processed {frame_index}/{total_frames} frames")

        cap.release()

        # Post-process and classify events
        classified_events = self._classify_events(event_candidates)

        # Generate comprehensive report with screenshots
        self._generate_report(classified_events, output_dir, video_path)

        return classified_events

    def _analyze_frame(self, frame, prev_frame, frame_index, fps):
        """
        Comprehensive frame analysis using multiple techniques.
        """
        results = {
            'frame_index': frame_index,
            'timestamp': frame_index / fps,
            'event_detected': False,
            'optical_flow': None,
            'brightness_analysis': None,
            'edge_analysis': None,
            'texture_analysis': None,
            'motion_vectors': None
        }

        gray = cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY)

        if prev_frame is not None:
            prev_gray = cv2.cvtColor(prev_frame, cv2.COLOR_BGR2GRAY)

            # 1. Advanced Optical Flow Analysis
            flow_results = self._advanced_optical_flow(prev_gray, gray)
            results['optical_flow'] = flow_results

            # 2. Brightness/Flash Detection
            brightness_results = self._analyze_brightness_change(
                prev_gray, gray)
            results['brightness_analysis'] = brightness_results

            # 3. Edge and Texture Analysis
            edge_results = self._analyze_edges_and_texture(prev_gray, gray)
            results['edge_analysis'] = edge_results

            # 4. Motion Vector Analysis
            motion_results = self._analyze_motion_patterns(
                flow_results['flow'])
            results['motion_vectors'] = motion_results

            # 5. Event Detection Logic
            results['event_detected'] = self._detect_event(
                flow_results, brightness_results, edge_results, motion_results
            )

        return results

    def _advanced_optical_flow(self, prev_gray, gray):
        """
        Enhanced optical flow analysis with multiple metrics.
        """
        # Use GPU optical flow if available, otherwise CPU
        if self.use_gpu and hasattr(cv2, 'cuda'):
            try:
                # Upload frames to GPU
                gpu_prev = cv2.cuda_GpuMat()
                gpu_curr = cv2.cuda_GpuMat()
                gpu_prev.upload(prev_gray)
                gpu_curr.upload(gray)

                # Create GPU optical flow object
                gpu_flow = cv2.cuda.FarnebackOpticalFlow_create()
                gpu_flow_result = cv2.cuda_GpuMat()

                # Compute flow on GPU
                gpu_flow.calc(gpu_prev, gpu_curr, gpu_flow_result)

                # Download result
                flow = gpu_flow_result.download()
            except:
                # Fallback to CPU if GPU fails
                flow = cv2.calcOpticalFlowFarneback(
                    prev_gray, gray, None, **self.config['flow_params']
                )
        else:
            # Compute dense optical flow on CPU
            flow = cv2.calcOpticalFlowFarneback(
                prev_gray, gray, None, **self.config['flow_params']
            )

        # Calculate flow magnitude and angle
        mag, ang = cv2.cartToPolar(flow[..., 0], flow[..., 1])

        # Compute divergence (expansion/contraction)
        ux, vy = flow[..., 0], flow[..., 1]
        dudx = np.gradient(ux, axis=1)
        dvdy = np.gradient(vy, axis=0)
        divergence = dudx + dvdy

        # Compute curl (rotation)
        dudy = np.gradient(ux, axis=0)
        dvdx = np.gradient(vy, axis=1)
        curl = dvdx - dudy

        # Smooth for noise reduction
        divergence_smooth = gaussian_filter(divergence, sigma=3)
        curl_smooth = gaussian_filter(curl, sigma=3)

        return {
            'flow': flow,  # Keep for internal use
            'magnitude': mag,  # Keep for internal use
            'angle': ang,  # Keep for internal use
            'divergence': divergence_smooth,  # Keep for internal use
            'curl': curl_smooth,  # Keep for internal use
            'max_magnitude': float(np.max(mag)),
            'mean_magnitude': float(np.mean(mag)),
            'max_divergence': float(np.max(np.abs(divergence_smooth)))
        }

    def _analyze_brightness_change(self, prev_gray, gray):
        """
        Analyze sudden brightness changes (flash detection).
        """
        # Compute brightness difference
        diff = cv2.absdiff(prev_gray, gray)

        # Global brightness metrics
        mean_diff = np.mean(diff)
        max_diff = np.max(diff)
        std_diff = np.std(diff)

        # Local brightness peaks
        thresh = cv2.threshold(diff, 0.8 * 255, 255, cv2.THRESH_BINARY)[1]
        contours, _ = cv2.findContours(
            thresh, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)

        flash_regions = []
        for contour in contours:
            area = cv2.contourArea(contour)
            if area > 100:  # Minimum flash size
                x, y, w, h = cv2.boundingRect(contour)
                flash_regions.append({
                    'bbox': (x, y, w, h),
                    'area': area,
                    'intensity': np.mean(diff[y:y+h, x:x+w])
                })

        return {
            'mean_brightness_change': float(mean_diff),
            'max_brightness_change': float(max_diff),
            'brightness_variance': float(std_diff),
            'flash_regions': flash_regions,
            'flash_detected': bool(len(flash_regions) > 0 and max_diff > 100)
        }

    def _analyze_edges_and_texture(self, prev_gray, gray):
        """
        Analyze edge changes and texture variations.
        """
        # Edge detection
        edges_prev = cv2.Canny(prev_gray, 50, 150)
        edges_curr = cv2.Canny(gray, 50, 150)
        edge_diff = cv2.absdiff(edges_prev, edges_curr)

        # Texture analysis using Local Binary Patterns concept
        def local_variance(img):
            kernel = np.ones((3, 3), np.float32) / 9
            mean = cv2.filter2D(img.astype(np.float32), -1, kernel)
            sqr_diff = (img.astype(np.float32) - mean) ** 2
            variance = cv2.filter2D(sqr_diff, -1, kernel)
            return variance

        texture_prev = local_variance(prev_gray)
        texture_curr = local_variance(gray)
        texture_diff = np.abs(texture_curr - texture_prev)

        return {
            'edge_change': float(np.mean(edge_diff)),
            'max_edge_change': float(np.max(edge_diff)),
            'texture_change': float(np.mean(texture_diff)),
            'max_texture_change': float(np.max(texture_diff)),
            'new_edges_ratio': float(np.sum(edges_curr) / max(np.sum(edges_prev), 1))
        }

    def _analyze_motion_patterns(self, flow):
        """
        Analyze motion patterns for characteristic signatures.
        """
        if flow is None:
            return {}

        # Calculate motion statistics
        mag, ang = cv2.cartToPolar(flow[..., 0], flow[..., 1])

        # Radial vs tangential motion analysis
        h, w = flow.shape[:2]
        center = (w//2, h//2)

        # Create coordinate grids
        y, x = np.ogrid[:h, :w]
        x_centered = x - center[0]
        y_centered = y - center[1]

        # Radial direction (pointing outward from center)
        radial_angle = np.arctan2(y_centered, x_centered)

        # Compare flow direction with radial direction
        angle_diff = np.abs(ang - radial_angle)
        angle_diff = np.minimum(angle_diff, 2*np.pi -
                                angle_diff)  # Wrap around

        radial_alignment = np.mean(np.cos(angle_diff))

        # Simplified motion analysis to avoid memory issues
        # Instead of clustering, use simpler statistical measures
        valid_flow = mag > np.percentile(mag, 75)  # Top 25% of motion
        n_clusters = 0  # Disable clustering to avoid memory issues

        # Alternative: count regions of high motion
        if np.any(valid_flow):
            # Simple count of high-motion regions
            n_clusters = int(np.sum(valid_flow) / 100)  # Rough estimate

        return {
            'radial_alignment': float(radial_alignment),
            'motion_clusters': int(n_clusters),
            'dominant_direction': float(np.mean(ang[mag > np.percentile(mag, 90)])),
            'motion_coherence': float(np.std(ang[mag > np.percentile(mag, 75)]))
        }

    def _detect_event(self, flow_results, brightness_results, edge_results, motion_results):
        """
        Multi-criteria event detection logic.
        """
        # Define thresholds
        high_motion = flow_results['max_magnitude'] > 10
        high_divergence = flow_results['max_divergence'] > 0.5
        flash_detected = brightness_results['flash_detected']
        significant_edges = edge_results['max_edge_change'] > 50

        # Combine criteria
        event_score = sum([
            high_motion * 0.3,
            high_divergence * 0.3,
            flash_detected * 0.25,
            significant_edges * 0.15
        ])

        return event_score > 0.5

    def _classify_events(self, event_candidates):
        """
        Classify detected events based on their characteristics.
        """
        classified_events = []

        for event in event_candidates:
            features = event['features']

            # Feature extraction for classification
            classification = self._extract_event_features(features)

            event['classification'] = classification
            classified_events.append(event)

        return classified_events

    def _extract_event_features(self, features):
        """
        Extract features for event classification.
        """
        # Initialize classification dict
        classification = {
            'event_type': 'unknown',
            'confidence': 0.0,
            'characteristics': {}
        }

        # Extract key metrics
        flow_data = features['optical_flow']
        brightness_data = features['brightness_analysis']
        edge_data = features['edge_analysis']
        motion_data = features['motion_vectors']

        # Characteristic analysis
        has_flash = brightness_data['flash_detected']
        high_divergence = flow_data['max_divergence'] > 1.0
        radial_motion = motion_data.get('radial_alignment', 0) > 0.7

        # Simple classification logic (expand based on research requirements)
        if has_flash and high_divergence and radial_motion:
            classification['event_type'] = 'explosive_event'
            classification['confidence'] = 0.8
        elif high_divergence and radial_motion:
            classification['event_type'] = 'impact_event'
            classification['confidence'] = 0.6
        elif has_flash:
            classification['event_type'] = 'flash_event'
            classification['confidence'] = 0.5

        # Store detailed characteristics (convert numpy types to Python types for JSON)
        classification['characteristics'] = {
            'flash_intensity': float(brightness_data['max_brightness_change']),
            'divergence_magnitude': float(flow_data['max_divergence']),
            'radial_alignment': float(motion_data.get('radial_alignment', 0)),
            'motion_coherence': float(motion_data.get('motion_coherence', 0)),
            'edge_changes': float(edge_data['max_edge_change'])
        }

        return classification

    def _update_results(self, frame_results):
        """Update global analysis results."""
        if frame_results['optical_flow']:
            self.results['motion_characteristics'].append({
                'frame': frame_results['frame_index'],
                'max_magnitude': frame_results['optical_flow']['max_magnitude'],
                'divergence': frame_results['optical_flow']['max_divergence']
            })

    def _generate_report(self, classified_events, output_dir, video_path):
        """
        Generate visual report with composite image of detected events.
        """
        # Create output directory
        if not os.path.exists(output_dir):
            os.makedirs(output_dir)

        # Summarize event types
        event_types = {}
        for event in classified_events:
            event_type = event['classification']['event_type']
            event_types[event_type] = event_types.get(event_type, 0) + 1

        # Generate composite image for all events
        self._generate_event_screenshots(
            classified_events, video_path, output_dir)

        # Create a simple text summary
        summary_path = os.path.join(output_dir, 'analysis_summary.txt')
        with open(summary_path, 'w') as f:
            f.write(f"Video Analysis Report\n")
            f.write("=" * 50 + "\n")
            f.write(
                f"Analysis Date: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
            f.write(f"Video File: {video_path}\n")
            f.write(f"Total Events Detected: {len(classified_events)}\n\n")

            f.write("Event Types Summary:\n")
            f.write("-" * 20 + "\n")
            for event_type, count in event_types.items():
                f.write(f"  {event_type}: {count} events\n")

            f.write(f"\nOutput Files:\n")
            f.write("-" * 15 + "\n")
            f.write(f"  Composite Image: events_composite.jpg\n")
            f.write(f"  Preview Image: events_preview.jpg\n")
            f.write(f"  This Summary: analysis_summary.txt\n")

            f.write(f"\nDetailed Events:\n")
            f.write("-" * 20 + "\n")
            for i, event in enumerate(classified_events):
                f.write(f"\nEvent {i+1}:\n")
                f.write(f"  Frame: {event['frame']}\n")
                f.write(f"  Timestamp: {event['timestamp']:.2f}s\n")
                f.write(f"  Type: {event['classification']['event_type']}\n")
                f.write(
                    f"  Confidence: {event['classification']['confidence']:.2f}\n")

        self.logger.info(
            f"Analysis complete. Found {len(classified_events)} events.")
        self.logger.info(f"Event types: {event_types}")
        self.logger.info(f"Results saved to: {output_dir}")

        return event_types

    def _generate_event_screenshots(self, classified_events, video_path, output_dir):
        """
        Generate a composite image showing all detected events in a grid layout.
        """
        if not classified_events:
            self.logger.info("No events to capture screenshots for.")
            return

        # Open video again for screenshot capture
        cap = cv2.VideoCapture(video_path)
        if not cap.isOpened():
            self.logger.error(
                f"Could not open video for screenshots: {video_path}")
            return

        self.logger.info(
            f"Generating composite image for {len(classified_events)} events...")

        # Collect all event frames
        event_frames = []
        for i, event in enumerate(classified_events):
            frame_number = event['frame']

            # Set video position to the event frame
            cap.set(cv2.CAP_PROP_POS_FRAMES, frame_number)
            ret, frame = cap.read()

            if ret:
                # Resize frame to thumbnail size
                thumbnail = cv2.resize(frame, (200, 150))
                # Add event info annotation
                annotated_thumbnail = self._annotate_thumbnail(
                    thumbnail, event, i+1)
                event_frames.append(annotated_thumbnail)
            else:
                self.logger.warning(
                    f"Could not read frame {frame_number} for event {i+1}")

        cap.release()

        if event_frames:
            # Create composite image
            composite_image = self._create_composite_image(
                event_frames, classified_events)

            # Save composite image
            composite_path = os.path.join(output_dir, "events_composite.jpg")
            cv2.imwrite(composite_path, composite_image)
            self.logger.info(f"Composite image saved: {composite_path}")

            # Also save a preview version
            preview_path = os.path.join(output_dir, "events_preview.jpg")
            preview_height = min(800, composite_image.shape[0])
            preview_width = int(
                composite_image.shape[1] * preview_height / composite_image.shape[0])
            preview = cv2.resize(
                composite_image, (preview_width, preview_height))
            cv2.imwrite(preview_path, preview)
            self.logger.info(f"Preview image saved: {preview_path}")

        self.logger.info(
            f"Event visualization complete. Saved to: {output_dir}")

    def _annotate_frame(self, frame, event, event_number):
        """
        Add annotations to the frame showing event information.
        """
        annotated = frame.copy()
        height, width = annotated.shape[:2]

        # Create overlay background
        overlay = annotated.copy()
        cv2.rectangle(overlay, (10, 10), (width-10, 120), (0, 0, 0), -1)
        cv2.addWeighted(overlay, 0.7, annotated, 0.3, 0, annotated)

        # Add text information
        font = cv2.FONT_HERSHEY_SIMPLEX
        font_scale = 0.6
        color = (0, 255, 255)  # Yellow text
        thickness = 2

        # Event information
        text_lines = [
            f"Event #{event_number}",
            f"Frame: {event['frame']} | Time: {event['timestamp']:.2f}s",
            f"Type: {event['classification']['event_type']}",
            f"Confidence: {event['classification']['confidence']:.2f}"
        ]

        y_offset = 30
        for line in text_lines:
            cv2.putText(annotated, line, (20, y_offset),
                        font, font_scale, color, thickness)
            y_offset += 25

        return annotated

    def _annotate_thumbnail(self, thumbnail, event, event_number):
        """
        Add compact annotations to thumbnail images.
        """
        annotated = thumbnail.copy()
        height, width = annotated.shape[:2]

        # Create compact overlay
        overlay = annotated.copy()
        cv2.rectangle(overlay, (0, height-30), (width, height), (0, 0, 0), -1)
        cv2.addWeighted(overlay, 0.8, annotated, 0.2, 0, annotated)

        # Add compact text
        font = cv2.FONT_HERSHEY_SIMPLEX
        font_scale = 0.4
        color = (0, 255, 255)  # Yellow text
        thickness = 1

        # Event info
        text = f"#{event_number} | Frame {event['frame']} | {event['timestamp']:.1f}s"
        cv2.putText(annotated, text, (5, height-10),
                    font, font_scale, color, thickness)

        return annotated

    def _create_composite_image(self, event_frames, classified_events):
        """
        Create a grid layout composite image from event thumbnails.
        """
        if not event_frames:
            return None

        # Calculate grid dimensions
        num_events = len(event_frames)
        cols = min(6, num_events)  # Maximum 6 columns
        rows = (num_events + cols - 1) // cols  # Ceiling division

        # Get thumbnail dimensions
        thumb_height, thumb_width = event_frames[0].shape[:2]

        # Create header space
        header_height = 80
        margin = 10

        # Calculate composite dimensions
        composite_width = cols * thumb_width + (cols + 1) * margin
        composite_height = header_height + rows * \
            thumb_height + (rows + 1) * margin

        # Create composite image with white background
        composite = np.ones(
            (composite_height, composite_width, 3), dtype=np.uint8) * 255

        # Add header
        self._add_header(composite, classified_events,
                         composite_width, header_height)

        # Place thumbnails in grid
        for i, frame in enumerate(event_frames):
            row = i // cols
            col = i % cols

            y_start = header_height + margin + row * (thumb_height + margin)
            y_end = y_start + thumb_height
            x_start = margin + col * (thumb_width + margin)
            x_end = x_start + thumb_width

            composite[y_start:y_end, x_start:x_end] = frame

        return composite

    def _add_header(self, composite, classified_events, width, header_height):
        """
        Add header information to the composite image.
        """
        # Header background
        cv2.rectangle(composite, (0, 0),
                      (width, header_height), (50, 50, 50), -1)

        # Title
        font = cv2.FONT_HERSHEY_SIMPLEX
        title = "Video Analysis Results - Detected Events"
        font_scale = 0.8
        color = (255, 255, 255)
        thickness = 2

        # Center the title
        text_size = cv2.getTextSize(title, font, font_scale, thickness)[0]
        x = (width - text_size[0]) // 2
        cv2.putText(composite, title, (x, 30), font,
                    font_scale, color, thickness)

        # Summary info
        summary_text = f"Total Events: {len(classified_events)} | Analysis Date: {datetime.now().strftime('%Y-%m-%d %H:%M')}"
        font_scale = 0.5
        thickness = 1
        text_size = cv2.getTextSize(
            summary_text, font, font_scale, thickness)[0]
        x = (width - text_size[0]) // 2
        cv2.putText(composite, summary_text, (x, 55),
                    font, font_scale, color, thickness)

# Usage example for PhD research


def run_analysis(video_path, output_dir='phd_analysis'):
    """
    Main function to run the advanced video analysis with GPU acceleration.
    """
    # Optimized configuration for speed and GPU usage
    config = {
        'flow_params': {
            'pyr_scale': 0.5,
            'levels': 2,  # Reduced for speed
            'winsize': 15,  # Balanced window size
            'iterations': 3,  # Balanced iterations
            'poly_n': 5,
            'poly_sigma': 1.2,
            'flags': cv2.OPTFLOW_FARNEBACK_GAUSSIAN
        },
        'frame_skip': 2,  # Skip frames for speed
        'roi_detection': {
            'brightness_threshold': 0.7,
            'size_threshold': 0.005,
            'temporal_window': 10
        },
        'use_gpu': True,
        'num_threads': min(8, mp.cpu_count()),
        'resize_factor': 0.7  # Resize to 70% for faster processing
    }

    # Initialize analyzer
    analyzer = AdvancedVideoAnalyzer(config)

    # Run analysis
    results = analyzer.analyze_video(video_path, output_dir)

    return results

# Example usage:
# results = run_analysis('path/to/research_video.mp4', 'research_output')
