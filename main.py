import cv2
import numpy as np
import matplotlib.pyplot as plt
import os
from scipy.ndimage import gaussian_filter
from scipy.signal import find_peaks
from sklearn.cluster import DBSCAN
import json
from datetime import datetime
import logging


class NumpyEncoder(json.JSONEncoder):
    """Custom JSON encoder for numpy types"""

    def default(self, obj):
        if isinstance(obj, np.ndarray):
            return obj.tolist()
        if isinstance(obj, np.integer):
            return int(obj)
        if isinstance(obj, np.floating):
            return float(obj)
        if isinstance(obj, np.bool_):
            return bool(obj)
        return super().default(obj)


class AdvancedVideoAnalyzer:
    """
    Advanced video analysis system for detecting and characterizing events
    based on multiple visual and temporal features.
    """

    def __init__(self, config=None):
        """
        Initialize the analyzer with configuration parameters.
        """
        self.config = config or self._default_config()
        self.setup_logging()

        # Analysis results storage
        self.results = {
            'temporal_features': [],
            'spatial_features': [],
            'motion_characteristics': [],
            'intensity_profiles': [],
            'event_classifications': []
        }

    def _default_config(self):
        """Default configuration parameters"""
        return {
            'flow_params': {
                'pyr_scale': 0.5,
                'levels': 3,
                'winsize': 15,
                'iterations': 3,
                'poly_n': 5,
                'poly_sigma': 1.2,
                'flags': 0
            },
            'frame_skip': 1,
            'roi_detection': {
                'brightness_threshold': 0.8,  # For flash detection
                'size_threshold': 0.01,       # Minimum event size
                'temporal_window': 5          # Frames to analyze around event
            },
            'classification_features': {
                'analyze_flash_duration': True,
                'analyze_smoke_pattern': True,
                'analyze_debris_trajectory': True,
                'analyze_shockwave_propagation': True
            }
        }

    def setup_logging(self):
        """Setup logging for the analysis"""
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s'
        )
        self.logger = logging.getLogger(__name__)

    def analyze_video(self, video_path="video/test.mp4", output_dir='advanced_analysis'):
        """
        Main analysis function that processes the entire video.
        """
        if not os.path.exists(output_dir):
            os.makedirs(output_dir)

        cap = cv2.VideoCapture(video_path)
        if not cap.isOpened():
            raise ValueError(f"Error opening video: {video_path}")

        # Get video properties
        fps = cap.get(cv2.CAP_PROP_FPS)
        total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))

        self.logger.info(
            f"Analyzing video: {total_frames} frames at {fps} FPS")

        # Initialize analysis components
        prev_frame = None
        frame_index = 0
        event_candidates = []

        while True:
            ret, frame = cap.read()
            if not ret:
                break

            if frame_index % self.config['frame_skip'] == 0:
                # Multi-modal analysis
                frame_results = self._analyze_frame(
                    frame, prev_frame, frame_index, fps
                )

                # Store results
                if frame_results['event_detected']:
                    event_candidates.append({
                        'frame': frame_index,
                        'timestamp': frame_index / fps,
                        'features': frame_results
                    })

                # Update analysis results
                self._update_results(frame_results)

                prev_frame = frame.copy()

            frame_index += 1

            if frame_index % 100 == 0:
                self.logger.info(
                    f"Processed {frame_index}/{total_frames} frames")

        cap.release()

        # Post-process and classify events
        classified_events = self._classify_events(event_candidates)

        # Generate comprehensive report
        self._generate_report(classified_events, output_dir)

        return classified_events

    def _analyze_frame(self, frame, prev_frame, frame_index, fps):
        """
        Comprehensive frame analysis using multiple techniques.
        """
        results = {
            'frame_index': frame_index,
            'timestamp': frame_index / fps,
            'event_detected': False,
            'optical_flow': None,
            'brightness_analysis': None,
            'edge_analysis': None,
            'texture_analysis': None,
            'motion_vectors': None
        }

        gray = cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY)

        if prev_frame is not None:
            prev_gray = cv2.cvtColor(prev_frame, cv2.COLOR_BGR2GRAY)

            # 1. Advanced Optical Flow Analysis
            flow_results = self._advanced_optical_flow(prev_gray, gray)
            results['optical_flow'] = flow_results

            # 2. Brightness/Flash Detection
            brightness_results = self._analyze_brightness_change(
                prev_gray, gray)
            results['brightness_analysis'] = brightness_results

            # 3. Edge and Texture Analysis
            edge_results = self._analyze_edges_and_texture(prev_gray, gray)
            results['edge_analysis'] = edge_results

            # 4. Motion Vector Analysis
            motion_results = self._analyze_motion_patterns(
                flow_results['flow'])
            results['motion_vectors'] = motion_results

            # 5. Event Detection Logic
            results['event_detected'] = self._detect_event(
                flow_results, brightness_results, edge_results, motion_results
            )

        return results

    def _advanced_optical_flow(self, prev_gray, gray):
        """
        Enhanced optical flow analysis with multiple metrics.
        """
        # Compute dense optical flow
        flow = cv2.calcOpticalFlowFarneback(
            prev_gray, gray, None, **self.config['flow_params']
        )

        # Calculate flow magnitude and angle
        mag, ang = cv2.cartToPolar(flow[..., 0], flow[..., 1])

        # Compute divergence (expansion/contraction)
        ux, vy = flow[..., 0], flow[..., 1]
        dudx = np.gradient(ux, axis=1)
        dvdy = np.gradient(vy, axis=0)
        divergence = dudx + dvdy

        # Compute curl (rotation)
        dudy = np.gradient(ux, axis=0)
        dvdx = np.gradient(vy, axis=1)
        curl = dvdx - dudy

        # Smooth for noise reduction
        divergence_smooth = gaussian_filter(divergence, sigma=3)
        curl_smooth = gaussian_filter(curl, sigma=3)

        return {
            'flow': flow,
            'magnitude': mag,
            'angle': ang,
            'divergence': divergence_smooth,
            'curl': curl_smooth,
            'max_magnitude': float(np.max(mag)),
            'mean_magnitude': float(np.mean(mag)),
            'max_divergence': float(np.max(np.abs(divergence_smooth)))
        }

    def _analyze_brightness_change(self, prev_gray, gray):
        """
        Analyze sudden brightness changes (flash detection).
        """
        # Compute brightness difference
        diff = cv2.absdiff(prev_gray, gray)

        # Global brightness metrics
        mean_diff = np.mean(diff)
        max_diff = np.max(diff)
        std_diff = np.std(diff)

        # Local brightness peaks
        thresh = cv2.threshold(diff, 0.8 * 255, 255, cv2.THRESH_BINARY)[1]
        contours, _ = cv2.findContours(
            thresh, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)

        flash_regions = []
        for contour in contours:
            area = cv2.contourArea(contour)
            if area > 100:  # Minimum flash size
                x, y, w, h = cv2.boundingRect(contour)
                flash_regions.append({
                    'bbox': (x, y, w, h),
                    'area': area,
                    'intensity': np.mean(diff[y:y+h, x:x+w])
                })

        return {
            'mean_brightness_change': float(mean_diff),
            'max_brightness_change': float(max_diff),
            'brightness_variance': float(std_diff),
            'flash_regions': flash_regions,
            'flash_detected': bool(len(flash_regions) > 0 and max_diff > 100)
        }

    def _analyze_edges_and_texture(self, prev_gray, gray):
        """
        Analyze edge changes and texture variations.
        """
        # Edge detection
        edges_prev = cv2.Canny(prev_gray, 50, 150)
        edges_curr = cv2.Canny(gray, 50, 150)
        edge_diff = cv2.absdiff(edges_prev, edges_curr)

        # Texture analysis using Local Binary Patterns concept
        def local_variance(img):
            kernel = np.ones((3, 3), np.float32) / 9
            mean = cv2.filter2D(img.astype(np.float32), -1, kernel)
            sqr_diff = (img.astype(np.float32) - mean) ** 2
            variance = cv2.filter2D(sqr_diff, -1, kernel)
            return variance

        texture_prev = local_variance(prev_gray)
        texture_curr = local_variance(gray)
        texture_diff = np.abs(texture_curr - texture_prev)

        return {
            'edge_change': float(np.mean(edge_diff)),
            'max_edge_change': float(np.max(edge_diff)),
            'texture_change': float(np.mean(texture_diff)),
            'max_texture_change': float(np.max(texture_diff)),
            'new_edges_ratio': float(np.sum(edges_curr) / max(np.sum(edges_prev), 1))
        }

    def _analyze_motion_patterns(self, flow):
        """
        Analyze motion patterns for characteristic signatures.
        """
        if flow is None:
            return {}

        # Calculate motion statistics
        mag, ang = cv2.cartToPolar(flow[..., 0], flow[..., 1])

        # Radial vs tangential motion analysis
        h, w = flow.shape[:2]
        center = (w//2, h//2)

        # Create coordinate grids
        y, x = np.ogrid[:h, :w]
        x_centered = x - center[0]
        y_centered = y - center[1]

        # Radial direction (pointing outward from center)
        radial_angle = np.arctan2(y_centered, x_centered)

        # Compare flow direction with radial direction
        angle_diff = np.abs(ang - radial_angle)
        angle_diff = np.minimum(angle_diff, 2*np.pi -
                                angle_diff)  # Wrap around

        radial_alignment = np.mean(np.cos(angle_diff))

        # Simplified motion analysis to avoid memory issues
        # Instead of clustering, use simpler statistical measures
        valid_flow = mag > np.percentile(mag, 75)  # Top 25% of motion
        n_clusters = 0  # Disable clustering to avoid memory issues

        # Alternative: count regions of high motion
        if np.any(valid_flow):
            # Simple count of high-motion regions
            n_clusters = int(np.sum(valid_flow) / 100)  # Rough estimate

        return {
            'radial_alignment': float(radial_alignment),
            'motion_clusters': int(n_clusters),
            'dominant_direction': float(np.mean(ang[mag > np.percentile(mag, 90)])),
            'motion_coherence': float(np.std(ang[mag > np.percentile(mag, 75)]))
        }

    def _detect_event(self, flow_results, brightness_results, edge_results, motion_results):
        """
        Multi-criteria event detection logic.
        """
        # Define thresholds
        high_motion = flow_results['max_magnitude'] > 10
        high_divergence = flow_results['max_divergence'] > 0.5
        flash_detected = brightness_results['flash_detected']
        significant_edges = edge_results['max_edge_change'] > 50

        # Combine criteria
        event_score = sum([
            high_motion * 0.3,
            high_divergence * 0.3,
            flash_detected * 0.25,
            significant_edges * 0.15
        ])

        return event_score > 0.5

    def _classify_events(self, event_candidates):
        """
        Classify detected events based on their characteristics.
        """
        classified_events = []

        for event in event_candidates:
            features = event['features']

            # Feature extraction for classification
            classification = self._extract_event_features(features)

            event['classification'] = classification
            classified_events.append(event)

        return classified_events

    def _extract_event_features(self, features):
        """
        Extract features for event classification.
        """
        # Initialize classification dict
        classification = {
            'event_type': 'unknown',
            'confidence': 0.0,
            'characteristics': {}
        }

        # Extract key metrics
        flow_data = features['optical_flow']
        brightness_data = features['brightness_analysis']
        edge_data = features['edge_analysis']
        motion_data = features['motion_vectors']

        # Characteristic analysis
        has_flash = brightness_data['flash_detected']
        high_divergence = flow_data['max_divergence'] > 1.0
        radial_motion = motion_data.get('radial_alignment', 0) > 0.7

        # Simple classification logic (expand based on research requirements)
        if has_flash and high_divergence and radial_motion:
            classification['event_type'] = 'explosive_event'
            classification['confidence'] = 0.8
        elif high_divergence and radial_motion:
            classification['event_type'] = 'impact_event'
            classification['confidence'] = 0.6
        elif has_flash:
            classification['event_type'] = 'flash_event'
            classification['confidence'] = 0.5

        # Store detailed characteristics (convert numpy types to Python types for JSON)
        classification['characteristics'] = {
            'flash_intensity': float(brightness_data['max_brightness_change']),
            'divergence_magnitude': float(flow_data['max_divergence']),
            'radial_alignment': float(motion_data.get('radial_alignment', 0)),
            'motion_coherence': float(motion_data.get('motion_coherence', 0)),
            'edge_changes': float(edge_data['max_edge_change'])
        }

        return classification

    def _update_results(self, frame_results):
        """Update global analysis results."""
        if frame_results['optical_flow']:
            self.results['motion_characteristics'].append({
                'frame': frame_results['frame_index'],
                'max_magnitude': frame_results['optical_flow']['max_magnitude'],
                'divergence': frame_results['optical_flow']['max_divergence']
            })

    def _generate_report(self, classified_events, output_dir):
        """
        Generate comprehensive analysis report.
        """
        report = {
            'analysis_timestamp': datetime.now().isoformat(),
            'total_events_detected': len(classified_events),
            'event_summary': {},
            'detailed_events': classified_events
        }

        # Summarize event types
        event_types = {}
        for event in classified_events:
            event_type = event['classification']['event_type']
            event_types[event_type] = event_types.get(event_type, 0) + 1

        report['event_summary'] = event_types

        # Save report
        with open(os.path.join(output_dir, 'analysis_report.json'), 'w') as f:
            json.dump(report, f, indent=2, cls=NumpyEncoder)

        self.logger.info(
            f"Analysis complete. Found {len(classified_events)} events.")
        self.logger.info(f"Event types: {event_types}")

        return report

# Usage example for PhD research


def run_analysis(video_path, output_dir='phd_analysis'):
    """
    Main function to run the advanced video analysis.
    """
    # Custom configuration for research
    config = {
        'flow_params': {
            'pyr_scale': 0.5,
            'levels': 4,  # More levels for better accuracy
            'winsize': 25,  # Larger window for stability
            'iterations': 5,  # More iterations
            'poly_n': 7,
            'poly_sigma': 1.5,
            'flags': cv2.OPTFLOW_FARNEBACK_GAUSSIAN
        },
        'frame_skip': 1,  # Analyze every frame for research accuracy
        'roi_detection': {
            'brightness_threshold': 0.7,
            'size_threshold': 0.005,
            'temporal_window': 10
        }
    }

    # Initialize analyzer
    analyzer = AdvancedVideoAnalyzer(config)

    # Run analysis
    results = analyzer.analyze_video(video_path, output_dir)

    return results

# Example usage:
# results = run_analysis('path/to/research_video.mp4', 'research_output')
