#!/usr/bin/env python3
"""
Simple test script for the video analysis program
"""

from main import AdvancedVideoAnalyzer
import time

def test_basic_functionality():
    """Test basic functionality of the analyzer"""
    print("Testing basic functionality...")
    
    # Create analyzer with minimal config for testing
    config = {
        'flow_params': {
            'pyr_scale': 0.5,
            'levels': 1,  # Minimal for speed
            'winsize': 10,
            'iterations': 1,
            'poly_n': 5,
            'poly_sigma': 1.2,
            'flags': 0
        },
        'frame_skip': 5,  # Skip many frames for speed
        'use_gpu': True,
        'num_threads': 8,
        'resize_factor': 0.3  # Very small for speed
    }
    
    analyzer = AdvancedVideoAnalyzer(config)
    
    print("<PERSON>ly<PERSON> created successfully!")
    print(f"GPU support: {analyzer.use_gpu}")
    
    # Test with just a few frames
    start_time = time.time()
    
    try:
        results = analyzer.analyze_video('video/test.mp4', 'test_output')
        end_time = time.time()
        
        print(f"Analysis completed in {end_time - start_time:.2f} seconds")
        print(f"Found {len(results)} events")
        
        return True
    except Exception as e:
        print(f"Error during analysis: {e}")
        return False

if __name__ == "__main__":
    success = test_basic_functionality()
    if success:
        print("✅ Test passed! The program is working correctly.")
    else:
        print("❌ Test failed!")
