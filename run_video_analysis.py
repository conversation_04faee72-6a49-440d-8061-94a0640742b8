#!/usr/bin/env python3
"""
Video Analysis Script with Screenshot Output
Optimized for GPU/CPU performance
"""

from main import run_analysis
import os
import sys
import time

def main():
    """Main function to run video analysis"""
    
    # Default video path
    video_path = "video/test.mp4"
    output_dir = "analysis_results"
    
    # Check if video file exists
    if not os.path.exists(video_path):
        print(f"❌ Error: Video file not found: {video_path}")
        print("Please make sure you have a video file in the 'video' directory.")
        return False
    
    print("🎬 Starting Advanced Video Analysis...")
    print(f"📹 Video: {video_path}")
    print(f"📁 Output: {output_dir}")
    print("⚡ Using optimized CPU processing with multi-threading")
    print("-" * 50)
    
    start_time = time.time()
    
    try:
        # Run the analysis
        results = run_analysis(video_path, output_dir)
        
        end_time = time.time()
        processing_time = end_time - start_time
        
        print("-" * 50)
        print("✅ Analysis Complete!")
        print(f"⏱️  Processing time: {processing_time:.2f} seconds")
        print(f"🎯 Events detected: {len(results)}")
        
        # Show output structure
        screenshots_dir = os.path.join(output_dir, "event_screenshots")
        if os.path.exists(screenshots_dir):
            screenshot_count = len([f for f in os.listdir(screenshots_dir) if f.endswith('.jpg')])
            print(f"📸 Screenshots saved: {screenshot_count}")
            print(f"📂 Screenshots location: {screenshots_dir}")
        
        summary_file = os.path.join(output_dir, "analysis_summary.txt")
        if os.path.exists(summary_file):
            print(f"📄 Summary report: {summary_file}")
        
        print("\n🔍 You can now:")
        print("1. View the screenshots in the event_screenshots folder")
        print("2. Read the analysis_summary.txt for detailed information")
        print("3. Use the screenshots to identify interesting events in your video")
        
        return True
        
    except Exception as e:
        print(f"❌ Error during analysis: {e}")
        return False

if __name__ == "__main__":
    success = main()
    if not success:
        sys.exit(1)
