#!/usr/bin/env python3
"""
Simple script to view the analysis results
"""

import cv2
import os
import sys

def view_results(output_dir="analysis_results"):
    """View the analysis results"""
    
    composite_path = os.path.join(output_dir, "events_composite.jpg")
    preview_path = os.path.join(output_dir, "events_preview.jpg")
    summary_path = os.path.join(output_dir, "analysis_summary.txt")
    
    print("🔍 Video Analysis Results Viewer")
    print("=" * 40)
    
    # Check if files exist
    if os.path.exists(composite_path):
        print(f"✅ Composite image found: {composite_path}")
        
        # Load and display image info
        img = cv2.imread(composite_path)
        if img is not None:
            height, width = img.shape[:2]
            print(f"   📐 Dimensions: {width} x {height} pixels")
            
            # Display the image
            print("   🖼️  Opening composite image...")
            cv2.imshow("Video Analysis - All Detected Events", img)
            print("   ⌨️  Press any key to close the image window")
            cv2.waitKey(0)
            cv2.destroyAllWindows()
        else:
            print("   ❌ Could not load composite image")
    else:
        print(f"❌ Composite image not found: {composite_path}")
    
    if os.path.exists(preview_path):
        print(f"✅ Preview image found: {preview_path}")
    else:
        print(f"❌ Preview image not found: {preview_path}")
    
    if os.path.exists(summary_path):
        print(f"✅ Summary report found: {summary_path}")
        
        # Show first few lines of summary
        print("\n📄 Summary Preview:")
        print("-" * 20)
        try:
            with open(summary_path, 'r') as f:
                lines = f.readlines()
                for i, line in enumerate(lines[:15]):  # Show first 15 lines
                    print(f"   {line.rstrip()}")
                if len(lines) > 15:
                    print(f"   ... ({len(lines) - 15} more lines)")
        except Exception as e:
            print(f"   ❌ Could not read summary: {e}")
    else:
        print(f"❌ Summary report not found: {summary_path}")
    
    print(f"\n📁 All results are in: {output_dir}")

if __name__ == "__main__":
    if len(sys.argv) > 1:
        output_dir = sys.argv[1]
    else:
        output_dir = "analysis_results"
    
    view_results(output_dir)
