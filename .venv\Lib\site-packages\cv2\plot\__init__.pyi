__all__: list[str] = []

import cv2
import cv2.typing
import typing as _typing


# Classes
class Plot2d(cv2.Algorithm):
    # Functions
    def setMinX(self, _plotMinX: float) -> None: ...

    def setMinY(self, _plotMinY: float) -> None: ...

    def setMaxX(self, _plotMaxX: float) -> None: ...

    def setMaxY(self, _plotMaxY: float) -> None: ...

    def setPlotLineWidth(self, _plotLineWidth: int) -> None: ...

    def setNeedPlotLine(self, _needPlotLine: bool) -> None: ...

    def setPlotLineColor(self, _plotLineColor: cv2.typing.Scalar) -> None: ...

    def setPlotBackgroundColor(self, _plotBackgroundColor: cv2.typing.Scalar) -> None: ...

    def setPlotAxisColor(self, _plotAxisColor: cv2.typing.Scalar) -> None: ...

    def setPlotGridColor(self, _plotGridColor: cv2.typing.Scalar) -> None: ...

    def setPlotTextColor(self, _plotTextColor: cv2.typing.Scalar) -> None: ...

    def setPlotSize(self, _plotSizeWidth: int, _plotSizeHeight: int) -> None: ...

    def setShowGrid(self, needShowGrid: bool) -> None: ...

    def setShowText(self, needShowText: bool) -> None: ...

    def setGridLinesNumber(self, gridLinesNumber: int) -> None: ...

    def setInvertOrientation(self, _invertOrientation: bool) -> None: ...

    def setPointIdxToPrint(self, pointIdx: int) -> None: ...

    @_typing.overload
    def render(self, _plotResult: cv2.typing.MatLike | None = ...) -> cv2.typing.MatLike: ...
    @_typing.overload
    def render(self, _plotResult: cv2.UMat | None = ...) -> cv2.UMat: ...

    @classmethod
    @_typing.overload
    def create(cls, data: cv2.typing.MatLike) -> Plot2d: ...
    @classmethod
    @_typing.overload
    def create(cls, data: cv2.UMat) -> Plot2d: ...
    @classmethod
    @_typing.overload
    def create(cls, dataX: cv2.typing.MatLike, dataY: cv2.typing.MatLike) -> Plot2d: ...
    @classmethod
    @_typing.overload
    def create(cls, dataX: cv2.UMat, dataY: cv2.UMat) -> Plot2d: ...



